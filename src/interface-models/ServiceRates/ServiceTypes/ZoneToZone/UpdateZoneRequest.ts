import { ZoneLocationSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneLocationSummary';

/**
 * Request to add or update any zone information including zone name and zone
 * locations for requested client. If zoneId is not provided then this API will
 * create and save zone identifier first, and then it will add zone locations
 * for created new zone. If zoneId and zoneName both are not null then it will
 * update zone name for the provided zone.
 */
export interface UpdateZoneRequest {
  /**
   * client id for which zone needs to be added or updated.
   */
  clientId: string;

  /**
   * zone id for zone for which zone information needs to be updated. It refers to zoneId from zoneIdentifiers and
   * ZoneLocations documents.
   * If zone id is not provided then zone is created in zone identifier first.
   */
  zoneId: number;

  /**
   * zone name for the zone. If it is provided then zone name for the zone is updated with provided zone name.
   */
  zoneName: string;

  /**
   * Zone locations with suburb details to be added to zone.
   */
  addedZoneLocations: ZoneLocationSummary[];

  /**
   * Zone locations with suburb details to be removed from zone.
   */
  deletedZoneLocations: ZoneLocationSummary[];
}
